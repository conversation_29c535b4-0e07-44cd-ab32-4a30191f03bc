<style>
    *{font-family:'Roboto';}
    .right{float:right;}
    p{font-size:14px}

</style>

<h3 style='font-size: 28px' ><PERSON> </h3>

**Futur diplômé en informatique** 

(514) 601 - 0081  
<EMAIL>   
Montréal, Québec, Canada 

<br>

## **À Propos de Moi**
---
Étudiant en informatique, j'aime le développement logiciel et enrichir mes compétences en explorant les technologies émergentes.

Je suis travaillant, ponctuel, sérieux, motivé, capable de s'adapter rapidement et de travailler efficacement en équipe comme de manière autonome

Mon objectif est de devenir développeur polyvalent et de contribuer à des projets tout en continuant à perfectionner mes compétences.

<br>

## **Expérience Professionnelle**
---

**Équipier** | McDonald's, Lachine <span class='right'> 2018 - 2021, 2023 - présent </span>  
- Service rapide et efficace dans un environnement à forte pression.  
- Collaboration en équipe pour maintenir un excellent service à la clientèle.  
- Maintien d’un environnement de travail propre et efficace.
  
<br>

**Développeur Visual Basic Excel** | Indépendant <span class='right'> 2025 </span> 
- Automatisation d'un classeur excel complet.   
- Collaboration avec un client pour adapter l’outil aux besoins.
- Apprentissage autonome de VBA Excel afin de répondre aux besoins du projet.

<br>

**Saisie de données** | Tourisme Abitibi-Témiscamingue <span class='right'> 2022 </span> 
- Entrée et validation d’informations dans une base de données.  
- Vérification de l’exactitude et de la cohérence des données.  
- Contribution au bon déroulement d’un projet administratif à grande échelle.  

<br>

**Journalier & Gardien de nuit** | Centre plein air du Lac Flavrian, Rouyn-Noranda <span class='right'> 2022  </span> 
- Surveillance d'un bâtiment et de la sécurité des vacanciers
- Tâches générales d’entretien et de soutiens à l'équipe.  
  
<br>

**Équipier** | Entrepôt Amazon, Lachine <span class='right'> 2021 </span>   
- Préparation et stockage des items dans l'inventaire
- Respect des délais et des objectifs de travail
- Résolution des problèmes physiques et techniques dans l'inventaire  
  
<br>

**Paysagiste** | Indépendant <span class='right'> 2015 - 2017 </span> 
- Tonte de gazon pour particuliers.  
- Gestion autonome des horaires et de la relation client.   

<br>

## **Formation**
---
| Diplôme                                        | Établissement              | Année |
|------------------------------------------------|-----------------------------|-------|
| Diplôme d’études collégiales en informatique   | Cégep du Vieux Montréal     | En cours  |
| Diplôme d’études collégiales en arts visuels   | Cégep du Vieux Montréal     | 2022  |
| Diplôme d’études Secondaires                   | Dalbé-Viau                  | 2019  |

<br>

## **Compétences Techniques**
---
**Langages & Outils**  
![Python](https://img.shields.io/badge/Python-3776AB?style=flat&logo=python&logoColor=white)
![HTML](https://img.shields.io/badge/HTML5-E34F26?style=flat&logo=html5&logoColor=white)
![CSS](https://img.shields.io/badge/CSS3-1572B6?style=flat&logo=css3&logoColor=white)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=flat&logo=javascript&logoColor=black)
![Java](https://img.shields.io/badge/Java-ED8B00?style=flat&logo=openjdk&logoColor=white)
![C++](https://img.shields.io/badge/C++-00599C?style=flat&logo=cplusplus&logoColor=white)
![Kotlin](https://img.shields.io/badge/Kotlin-7F52FF?style=flat&logo=kotlin&logoColor=white)
![PHP](https://img.shields.io/badge/PHP-777BB4?style=flat&logo=php&logoColor=white)

**Bases de données**  
![PostgreSQL](https://img.shields.io/badge/PostgreSQL-316192?style=flat&logo=postgresql&logoColor=white)
![MySQL](https://img.shields.io/badge/MySQL-4479A1?style=flat&logo=mysql&logoColor=white)
![SQLite](https://img.shields.io/badge/SQLite-003B57?style=flat&logo=sqlite&logoColor=white)
![MongoDB](https://img.shields.io/badge/MongoDB-47A248?style=flat&logo=mongodb&logoColor=white)
![Neo4j](https://img.shields.io/badge/Neo4j-008CC1?style=flat&logo=neo4j&logoColor=white)

**Frameworks & Librairies**  
![React Native](https://img.shields.io/badge/React%20Native-61DAFB?style=flat&logo=react&logoColor=black)
![PySide](https://img.shields.io/badge/PySide-3776AB?style=flat&logo=qt&logoColor=white)
![Tkinter](https://img.shields.io/badge/Tkinter-FF6F00?style=flat&logo=python&logoColor=white)
![NumPy](https://img.shields.io/badge/NumPy-013243?style=flat&logo=numpy&logoColor=white)
![PyQt](https://img.shields.io/badge/PyQt-41CD52?style=flat&logo=qt&logoColor=white)

**Outils & Logiciels**  
![GitHub](https://img.shields.io/badge/GitHub-181717?style=flat&logo=github&logoColor=white)
![Visual Studio](https://img.shields.io/badge/Visual%20Studio-5C2D91?style=flat&logo=visualstudio&logoColor=white)
![Android Studio](https://img.shields.io/badge/Android%20Studio-3DDC84?style=flat&logo=androidstudio&logoColor=white)
![Excel](https://img.shields.io/badge/Excel-217346?style=flat&logo=microsoft-excel&logoColor=white)
![Word](https://img.shields.io/badge/Word-2B579A?style=flat&logo=microsoft-word&logoColor=white)
![Powershell](https://img.shields.io/badge/Powershell-5391FE?style=flat&logo=powershell&logoColor=white)

**Architecture**  
`MVC`

<br>

## **Langues**
---
- Français  
- Anglais  

<br>

## **Référence**
---
**Mcdonald's JHC**
- Téléphone: (514) 637 - 5954
- Courriel: <EMAIL> 
